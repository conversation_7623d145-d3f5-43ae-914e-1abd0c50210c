"""
Document Intelligence Agent - Advanced document processing and semantic search
"""
import asyncio
import logging
import numpy as np
from typing import Dict, Any, List, Optional
from pathlib import Path
import ollama
import hashlib
import re

from .base_agent import BaseAgent, Message, MessageType
from config.settings import OLLAMA_CONFIG

# Temporarily disable ChromaDB and sentence-transformers for compatibility
try:
    import chromadb
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    chromadb = None

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    SentenceTransformer = None

logger = logging.getLogger(__name__)


class DocumentIntelligenceAgent(BaseAgent):
    """Agent specialized in document processing and semantic search"""
    
    def __init__(self):
        super().__init__(
            agent_id="document_intelligence",
            name="Document Intelligence Agent",
            role="Advanced document processing and semantic search specialist",
            capabilities=[
                "semantic_search",
                "document_embedding",
                "text_extraction",
                "document_classification",
                "content_summarization"
            ]
        )

        self.embedding_model = None
        self.chroma_client = None
        self.collection = None
        self.ollama_client = None
        self.documents_store = []  # Simple in-memory storage for now
        self.initialized = False  # Track initialization status

        # Register message handlers
        self.message_handlers[MessageType.REQUEST] = self._handle_request

    async def start(self):
        """Start the agent and initialize components"""
        await super().start()
        await self._initialize_components()

    async def _initialize_components(self):
        """Initialize embedding model, vector database, and Ollama client"""
        try:
            # Initialize embedding model (if available)
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                self.logger.info("Loading embedding model...")
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            else:
                self.logger.warning("Sentence transformers not available, using keyword-based search")

            # Initialize ChromaDB (if available)
            if CHROMADB_AVAILABLE:
                self.logger.info("Initializing ChromaDB...")
                try:
                    from config.settings import VECTOR_DB_CONFIG
                    self.chroma_client = chromadb.PersistentClient(
                        path=VECTOR_DB_CONFIG["persist_directory"]
                    )

                    # Get or create collection
                    try:
                        self.collection = self.chroma_client.get_collection(
                            name=VECTOR_DB_CONFIG["collection_name"]
                        )
                        self.logger.info("Loaded existing ChromaDB collection")
                    except:
                        self.collection = self.chroma_client.create_collection(
                            name=VECTOR_DB_CONFIG["collection_name"],
                            metadata={"description": "Neurocovid medical documents"}
                        )
                        self.logger.info("Created new ChromaDB collection")
                except Exception as e:
                    self.logger.warning(f"ChromaDB initialization failed: {e}, using in-memory storage")
                    self.chroma_client = None
                    self.collection = None
            else:
                self.logger.warning("ChromaDB not available, using in-memory storage")

            # Initialize Ollama client
            self.ollama_client = ollama.Client(host=OLLAMA_CONFIG["base_url"])

            self.initialized = True
            self.logger.info("Document Intelligence Agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Error initializing Document Intelligence Agent: {e}")
            self.initialized = False
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming request"""
        action = request.get("action", "")
        inputs = request.get("inputs", {})
        
        if action == "semantic_search":
            return await self._semantic_search(inputs)
        elif action == "add_documents":
            return await self._add_documents(inputs)
        elif action == "summarize_content":
            return await self._summarize_content(inputs)
        elif action == "classify_document":
            return await self._classify_document(inputs)
        else:
            return {"error": f"Unknown action: {action}"}
    
    async def _semantic_search(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform semantic search on documents"""
        try:
            query = inputs.get("query", "")
            max_results = inputs.get("max_results", 5)

            if not query:
                return {"error": "Query is required"}

            # Use ChromaDB if available, otherwise fallback to keyword search
            if self.collection and self.embedding_model:
                return await self._chromadb_search(query, max_results)
            else:
                return await self._keyword_search(query, max_results)

        except Exception as e:
            self.logger.error(f"Error in semantic search: {e}")
            return {"error": str(e)}

    async def _chromadb_search(self, query: str, max_results: int) -> Dict[str, Any]:
        """Search using ChromaDB and embeddings"""
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query])[0].tolist()

            # Search in ChromaDB
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=max_results,
                include=["documents", "metadatas", "distances"]
            )

            # Process results
            documents = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    metadata = results["metadatas"][0][i] if results["metadatas"] else {}
                    distance = results["distances"][0][i] if results["distances"] else 0

                    documents.append({
                        "content": doc,
                        "metadata": metadata,
                        "relevance_score": 1 - distance,  # Convert distance to relevance
                        "filename": metadata.get("filename", "unknown"),
                        "page": metadata.get("page", 0)
                    })

            # Generate summary if documents found
            summary = ""
            if documents:
                summary = await self._generate_search_summary(query, documents)

            return {
                "query": query,
                "documents": documents,
                "summary": summary,
                "total_results": len(documents),
                "search_method": "semantic"
            }

        except Exception as e:
            self.logger.error(f"Error in ChromaDB search: {e}")
            return {"error": str(e)}

    async def _keyword_search(self, query: str, max_results: int) -> Dict[str, Any]:
        """Fallback keyword-based search"""
        try:
            query_lower = query.lower()
            query_words = [word.strip() for word in query_lower.split() if len(word.strip()) > 2]
            results = []

            # Medical keywords for neurocovid
            medical_keywords = [
                'neurológic', 'covid', 'sars-cov-2', 'anosmia', 'ageusia', 'cefaleia',
                'encefal', 'meningite', 'avc', 'delirium', 'confusão', 'convulsão',
                'neuropatia', 'mielite', 'síndrome', 'manifestação', 'sintoma',
                'tratamento', 'diagnóstico', 'fisiopatologia', 'mecanismo'
            ]

            for doc in self.documents_store:
                text_lower = doc["text"].lower()
                score = 0

                # Score based on query words
                for word in query_words:
                    if word in text_lower:
                        exact_matches = text_lower.count(word)
                        score += exact_matches * 3

                        # Partial word matches
                        for text_word in text_lower.split():
                            if word in text_word and word != text_word:
                                score += 1

                # Bonus for medical keywords
                for keyword in medical_keywords:
                    if keyword in text_lower:
                        score += text_lower.count(keyword) * 2

                # Bonus for multiple query words
                words_found = sum(1 for word in query_words if word in text_lower)
                if words_found > 1:
                    score += words_found * 2

                if score > 0:
                    results.append({
                        "content": doc["text"],
                        "filename": doc.get("filename", "unknown"),
                        "page": doc.get("page", 0),
                        "relevance_score": score / 10,  # Normalize score
                        "metadata": doc
                    })

            # Sort by relevance
            results.sort(key=lambda x: x["relevance_score"], reverse=True)
            documents = results[:max_results]

            # Generate summary if documents found
            summary = ""
            if documents:
                summary = await self._generate_search_summary(query, documents)

            return {
                "query": query,
                "documents": documents,
                "summary": summary,
                "total_results": len(documents),
                "search_method": "keyword"
            }

        except Exception as e:
            self.logger.error(f"Error in keyword search: {e}")
            return {"error": str(e)}
    
    async def _add_documents(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Add documents to the vector database"""
        try:
            documents = inputs.get("documents", [])

            if not documents:
                return {"error": "No documents provided"}

            # Add to in-memory storage first
            for doc in documents:
                self.documents_store.append(doc)

            # Try to add to ChromaDB if available
            if self.collection and self.embedding_model:
                try:
                    # Process documents for ChromaDB
                    texts = []
                    metadatas = []
                    ids = []

                    for i, doc in enumerate(documents):
                        text = doc.get("text", "")
                        metadata = {
                            "filename": doc.get("filename", "unknown"),
                            "page": doc.get("page", 0),
                            "chunk_id": doc.get("chunk_id", f"chunk_{i}"),
                            "added_timestamp": str(asyncio.get_event_loop().time())
                        }

                        texts.append(text)
                        metadatas.append(metadata)
                        ids.append(metadata["chunk_id"])

                    # Generate embeddings
                    embeddings = self.embedding_model.encode(texts).tolist()

                    # Add to ChromaDB
                    self.collection.add(
                        documents=texts,
                        metadatas=metadatas,
                        embeddings=embeddings,
                        ids=ids
                    )

                    self.logger.info(f"Added {len(documents)} documents to vector database")
                    storage_method = "chromadb"

                except Exception as e:
                    self.logger.warning(f"Failed to add to ChromaDB: {e}, using in-memory storage")
                    storage_method = "memory"
            else:
                storage_method = "memory"
                self.logger.info(f"Added {len(documents)} documents to in-memory storage")

            return {
                "status": "success",
                "documents_added": len(documents),
                "storage_method": storage_method,
                "message": f"Successfully added {len(documents)} documents using {storage_method}"
            }

        except Exception as e:
            self.logger.error(f"Error adding documents: {e}")
            return {"error": str(e)}
    
    async def _summarize_content(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Summarize document content"""
        try:
            content = inputs.get("content", "")
            summary_type = inputs.get("summary_type", "general")
            
            if not content:
                return {"error": "Content is required"}
            
            # Create summary prompt based on type
            if summary_type == "medical":
                prompt = f"""
                Summarize the following medical content focusing on:
                - Key medical findings
                - Symptoms and manifestations
                - Treatment approaches
                - Clinical significance
                
                Content: {content}
                
                Medical Summary:
                """
            else:
                prompt = f"""
                Provide a concise summary of the following content:
                
                {content}
                
                Summary:
                """
            
            # Generate summary using Ollama
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.3, "num_predict": 512}
            )
            
            summary = response["message"]["content"]
            
            return {
                "original_length": len(content),
                "summary": summary,
                "summary_length": len(summary),
                "compression_ratio": len(summary) / len(content)
            }
            
        except Exception as e:
            self.logger.error(f"Error summarizing content: {e}")
            return {"error": str(e)}
    
    async def _classify_document(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Classify document type and medical relevance"""
        try:
            content = inputs.get("content", "")
            
            if not content:
                return {"error": "Content is required"}
            
            # Medical classification keywords
            medical_categories = {
                "neurology": ["neurological", "brain", "nervous system", "neuron", "cognitive"],
                "covid": ["covid", "sars-cov-2", "coronavirus", "pandemic"],
                "symptoms": ["symptom", "manifestation", "clinical", "patient"],
                "treatment": ["treatment", "therapy", "medication", "intervention"],
                "research": ["study", "research", "analysis", "findings", "results"]
            }
            
            # Calculate relevance scores
            content_lower = content.lower()
            scores = {}
            
            for category, keywords in medical_categories.items():
                score = sum(content_lower.count(keyword) for keyword in keywords)
                scores[category] = score
            
            # Determine primary category
            primary_category = max(scores, key=scores.get) if scores else "general"
            
            # Calculate overall medical relevance
            total_medical_keywords = sum(scores.values())
            medical_relevance = min(total_medical_keywords / 10, 1.0)  # Normalize to 0-1
            
            return {
                "primary_category": primary_category,
                "category_scores": scores,
                "medical_relevance": medical_relevance,
                "is_medical": medical_relevance > 0.3
            }
            
        except Exception as e:
            self.logger.error(f"Error classifying document: {e}")
            return {"error": str(e)}
    
    async def _generate_search_summary(self, query: str, documents: List[Dict]) -> str:
        """Generate a summary of search results"""
        try:
            # Combine top documents
            combined_content = "\n\n".join([
                f"[{doc['filename']}, page {doc['page']}]: {doc['content'][:500]}..."
                for doc in documents[:3]
            ])
            
            prompt = f"""
            Based on the following medical documents, provide a comprehensive answer to the query: "{query}"
            
            Documents:
            {combined_content}
            
            Please provide a detailed medical response with proper citations.
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.2, "num_predict": 1024}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating search summary: {e}")
            return "Error generating summary"
    
    async def _handle_request(self, message: Message):
        """Handle incoming request"""
        try:
            # Check if agent is initialized
            if not self.initialized:
                self.logger.warning("Agent not fully initialized, waiting...")
                # Wait a bit for initialization to complete
                for _ in range(10):  # Wait up to 5 seconds
                    if self.initialized:
                        break
                    await asyncio.sleep(0.5)

                if not self.initialized:
                    error_response = Message(
                        sender_id=self.agent_id,
                        receiver_id=message.sender_id,
                        message_type=MessageType.ERROR,
                        content={"error": "Agent not initialized"},
                        correlation_id=message.correlation_id
                    )
                    await self.send_message(error_response)
                    return

            result = await self.process_request(message.content)

            response = Message(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.RESPONSE,
                content=result,
                correlation_id=message.correlation_id
            )

            await self.send_message(response)

        except Exception as e:
            self.logger.error(f"Error handling request: {e}")
            error_response = Message(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.ERROR,
                content={"error": str(e)},
                correlation_id=message.correlation_id
            )
            await self.send_message(error_response)
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the document collection"""
        try:
            if self.collection:
                count = self.collection.count()
                return {
                    "total_documents": count,
                    "collection_name": "neurocovid_articles",
                    "storage_method": "chromadb"
                }
            else:
                return {
                    "total_documents": len(self.documents_store),
                    "collection_name": "in_memory_storage",
                    "storage_method": "memory"
                }
        except Exception as e:
            return {"error": str(e)}
