"""
Aplicação Mínima para Testar Streamlit
"""
import streamlit as st

st.set_page_config(
    page_title="Teste Multi-Agente",
    page_icon="🧠",
    layout="wide"
)

st.title("🧠 Sistema Multi-Agente Médico - Teste")

st.write("✅ Streamlit está funcionando!")

# Teste básico de importação
try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from agents.base_agent import BaseAgent
    st.success("✅ Importação de agentes funcionando!")
    
    from security.medical_security import compliance_manager
    st.success("✅ Sistema de segurança funcionando!")
    
    st.info("🎉 Sistema básico está operacional!")
    
    # Teste simples
    if st.button("🧪 Testar Sistema"):
        with st.spinner("Testando..."):
            try:
                # Teste de autenticação
                auth_result = compliance_manager.access_control.authenticate_user(
                    "test_user", "physician", {}
                )
                
                if auth_result["authenticated"]:
                    st.success("✅ Autenticação funcionando")
                    
                    # Teste de dados médicos
                    test_data = "Paciente com anosmia pós-COVID"
                    result = compliance_manager.process_medical_data(
                        data=test_data,
                        user_session=auth_result["session_token"],
                        operation="read"
                    )
                    
                    if result["compliant"]:
                        st.success("✅ Processamento de dados médicos funcionando")
                        st.json(result["classification"])
                    else:
                        st.error(f"❌ Erro: {result.get('error')}")
                else:
                    st.error("❌ Falha na autenticação")
                    
            except Exception as e:
                st.error(f"❌ Erro no teste: {e}")
    
    st.markdown("---")
    st.markdown("### 🚀 Próximos Passos:")
    st.markdown("1. ✅ Streamlit funcionando")
    st.markdown("2. ✅ Importações básicas OK")
    st.markdown("3. 🔄 Execute: `streamlit run simple_multi_agent_app.py`")
    
except ImportError as e:
    st.error(f"❌ Erro de importação: {e}")
    st.markdown("### 🔧 Para resolver:")
    st.code("pip install -r requirements.txt")
    
except Exception as e:
    st.error(f"❌ Erro geral: {e}")
    st.markdown("### 🔧 Verifique:")
    st.markdown("- Se está no diretório correto")
    st.markdown("- Se todas as dependências estão instaladas")
    st.markdown("- Se o Ollama está rodando")
